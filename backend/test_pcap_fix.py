#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试PostgreSQL抓包文件生成bug修复效果
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime

# 添加backend目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.test_case_execution_agent_service import TestCaseExecutionAgentService
from services.database_config_service import database_config_service

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 测试用例B（外键约束与级联操作测试）
TEST_CASE_B = {
    "id": "7846029c-5db4-4e9d-baa5-0d0b427a2147",
    "title": "外键约束与级联操作测试",
    "module": "数据完整性",
    "priority": "medium",
    "status": "completed",
    "preconditions": "数据库连接信息: localhost:5432/testdb, 用户: testuser, 初始状态: 数据库为空",
    "test_steps": [
        {
            "step_number": 1,
            "action": "创建父表和子表",
            "expected_result": "表创建成功，外键约束生效",
            "test_data": "CREATE TABLE departments (dept_id SERIAL PRIMARY KEY, dept_name VARCHAR(50) UNIQUE NOT NULL); CREATE TABLE employees (emp_id SERIAL PRIMARY KEY, emp_name VARCHAR(100) NOT NULL, dept_id INTEGER REFERENCES departments(dept_id) ON DELETE CASCADE);",
            "sql_statement": None,
            "expected_packets": None
        },
        {
            "step_number": 2,
            "action": "插入测试数据",
            "expected_result": "数据插入成功",
            "test_data": "INSERT INTO departments (dept_name) VALUES ('HR'), ('IT'), ('Finance'); INSERT INTO employees (emp_name, dept_id) VALUES ('Alice', 1), ('Bob', 2), ('Charlie', 1);",
            "sql_statement": None,
            "expected_packets": None
        },
        {
            "step_number": 3,
            "action": "测试外键约束",
            "expected_result": "插入失败，违反外键约束",
            "test_data": "INSERT INTO employees (emp_name, dept_id) VALUES ('David', 99);",
            "sql_statement": None,
            "expected_packets": None
        },
        {
            "step_number": 4,
            "action": "测试级联删除",
            "expected_result": "删除部门时相关员工也被删除",
            "test_data": "DELETE FROM departments WHERE dept_name = 'HR'; SELECT * FROM employees; -- 应只返回Bob的记录",
            "sql_statement": None,
            "expected_packets": None
        },
        {
            "step_number": 5,
            "action": "清理测试环境",
            "expected_result": "表删除成功",
            "test_data": "DROP TABLE employees; DROP TABLE departments;",
            "sql_statement": None,
            "expected_packets": None
        }
    ],
    "expected_result": "外键约束和级联操作按预期工作",
    "test_data": None,
    "tags": ["外键", "级联"],
    "author": "AI生成",
    "database_type": "postgresql"
}

async def test_pcap_fix():
    """测试pcap文件生成修复效果"""
    logger.info("=" * 60)
    logger.info("开始测试PostgreSQL抓包文件生成bug修复效果")
    logger.info("=" * 60)
    
    try:
        # 1. 初始化执行器
        logger.info("1. 初始化测试用例执行器...")
        executor = TestCaseExecutionAgentService()
        
        # 2. 获取PostgreSQL配置
        logger.info("2. 获取PostgreSQL数据库配置...")
        configs = await database_config_service.get_configs_by_type("postgresql")
        if not configs:
            logger.error("未找到PostgreSQL数据库配置")
            return False
        
        config = configs[0]
        logger.info(f"使用配置: {config.host}:{config.port}/{config.database_name}")
        
        # 3. 清理旧的pcap文件
        logger.info("3. 清理旧的pcap文件...")
        from utils.path_manager import path_manager
        captures_dir = path_manager.get_captures_dir()
        
        # 删除今天的postgres pcap文件
        today = datetime.now().strftime("%Y%m%d")
        import glob
        old_files = glob.glob(os.path.join(captures_dir, f"postgres_local_capture_{today}_*.pcap"))
        for old_file in old_files:
            try:
                os.remove(old_file)
                logger.info(f"删除旧文件: {os.path.basename(old_file)}")
            except Exception as e:
                logger.warning(f"删除文件失败: {e}")
        
        # 4. 执行测试用例
        logger.info("4. 执行测试用例B（外键约束与级联操作测试）...")
        test_case_json = json.dumps(TEST_CASE_B, ensure_ascii=False)
        
        result = await executor.execute_test_case(
            test_case_json=test_case_json,
            config_id=config.id,
            capture_enabled=True,
            test_case_id=TEST_CASE_B["id"],
            use_c_executor=False
        )
        
        # 5. 分析执行结果
        logger.info("5. 分析执行结果...")
        logger.info(f"执行成功: {result.get('success', False)}")
        
        if result.get('success'):
            capture_files = result.get('all_capture_files', [])
            logger.info(f"生成的抓包文件数量: {len(capture_files)}")
            
            # 验证文件是否真实存在
            valid_files = 0
            for capture_file in capture_files:
                if capture_file:
                    full_path = path_manager.resolve_capture_file_path(capture_file)
                    if os.path.exists(full_path):
                        file_size = os.path.getsize(full_path)
                        logger.info(f"✅ 文件存在: {capture_file} ({file_size} bytes)")
                        valid_files += 1
                    else:
                        logger.error(f"❌ 文件不存在: {capture_file} (路径: {full_path})")
            
            logger.info(f"有效文件数量: {valid_files}/{len(capture_files)}")
            
            if valid_files == len(capture_files) and valid_files > 0:
                logger.info("🎉 修复成功！所有抓包文件都真实存在")
                return True
            else:
                logger.error("❌ 修复失败！仍有文件不存在或没有生成文件")
                return False
        else:
            error = result.get('error', 'Unknown error')
            logger.error(f"执行失败: {error}")
            return False
            
    except Exception as e:
        logger.error(f"测试过程中发生异常: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

async def main():
    """主函数"""
    success = await test_pcap_fix()
    
    if success:
        logger.info("✅ 测试通过：PostgreSQL抓包文件生成bug已修复")
        sys.exit(0)
    else:
        logger.error("❌ 测试失败：PostgreSQL抓包文件生成bug仍存在")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
