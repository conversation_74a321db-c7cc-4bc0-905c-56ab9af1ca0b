#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
抓包验证管理器 - 统一管理抓包任务的验证流程
"""

import os
import glob
import json
import time
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime

from .pcap_validation_service import PCAPValidationService, ValidationResult, ValidationRule, ExpectedSQL
from .pcap_sql_analyzer import DatabaseType, SQLType

# 配置日志
logger = logging.getLogger(__name__)

@dataclass
class CaptureTask:
    """抓包任务信息"""
    task_id: str
    database_type: str
    expected_operations: List[str]  # 预期的操作类型，如 ['SELECT', 'INSERT']
    pcap_files: List[str]
    start_time: float
    end_time: Optional[float] = None
    validation_result: Optional[Dict[str, Any]] = None

class CaptureValidationManager:
    """抓包验证管理器"""
    
    def __init__(self, captures_dir: str = "captures"):
        """初始化管理器"""
        self.captures_dir = captures_dir
        self.validation_service = PCAPValidationService()
        self.active_tasks = {}  # task_id -> CaptureTask
        self.completed_tasks = {}  # task_id -> CaptureTask
        
        # 确保captures目录存在
        os.makedirs(self.captures_dir, exist_ok=True)
    
    def start_capture_task(self, task_id: str, database_type: str, 
                          expected_operations: List[str]) -> CaptureTask:
        """开始一个抓包任务"""
        task = CaptureTask(
            task_id=task_id,
            database_type=database_type.lower(),
            expected_operations=expected_operations,
            pcap_files=[],
            start_time=time.time()
        )
        
        self.active_tasks[task_id] = task
        logger.info(f"开始抓包任务: {task_id}, 数据库类型: {database_type}, 预期操作: {expected_operations}")
        
        return task
    
    def add_pcap_file_to_task(self, task_id: str, pcap_file: str):
        """向任务添加PCAP文件"""
        if task_id in self.active_tasks:
            # 确保文件路径正确，支持多种可能的路径格式
            if not os.path.isabs(pcap_file):
                # 尝试多种可能的路径组合来找到实际文件
                possible_paths = [
                    pcap_file,  # 原始路径
                    os.path.join(self.captures_dir, pcap_file),  # captures_dir + 文件名
                    os.path.join("backend/backend/captures", pcap_file),  # 实际存储路径
                    os.path.join("backend/captures", pcap_file),  # 标准路径
                ]

                # 如果文件路径已经包含captures目录，直接使用
                if any(pcap_file.startswith(path) for path in ["backend/captures/", "backend/backend/captures/"]):
                    possible_paths.insert(0, pcap_file)

                # 找到第一个存在的文件路径
                final_path = pcap_file
                for path in possible_paths:
                    if os.path.exists(path):
                        final_path = path
                        break

                pcap_file = final_path

            self.active_tasks[task_id].pcap_files.append(pcap_file)
            logger.info(f"任务 {task_id} 添加PCAP文件: {pcap_file} (存在: {os.path.exists(pcap_file)})")
        else:
            logger.warning(f"任务 {task_id} 不存在或已完成")
    
    def complete_capture_task(self, task_id: str, auto_validate: bool = True) -> Optional[Dict[str, Any]]:
        """完成抓包任务并执行验证"""
        if task_id not in self.active_tasks:
            logger.warning(f"任务 {task_id} 不存在或已完成")
            return None
        
        task = self.active_tasks[task_id]
        task.end_time = time.time()
        
        logger.info(f"完成抓包任务: {task_id}, 共收集 {len(task.pcap_files)} 个PCAP文件")
        
        validation_result = None
        if auto_validate:
            validation_result = self.validate_task(task)
            task.validation_result = validation_result
        
        # 移动到已完成任务
        self.completed_tasks[task_id] = task
        del self.active_tasks[task_id]
        
        return validation_result
    
    def validate_task(self, task: CaptureTask) -> Dict[str, Any]:
        """验证抓包任务"""
        logger.info(f"开始验证任务: {task.task_id}")
        
        # 创建自定义验证规则
        custom_rule = self._create_validation_rule_for_task(task)
        
        # 验证所有PCAP文件
        validation_results = []
        for pcap_file in task.pcap_files:
            # 使用路径管理器解析文件路径
            from utils.path_manager import path_manager

            if not os.path.isabs(pcap_file):
                resolved_path = path_manager.resolve_capture_file_path(pcap_file)
            else:
                resolved_path = pcap_file

            logger.info(f"验证任务文件路径解析: {pcap_file} -> {resolved_path}")

            if os.path.exists(resolved_path):
                result = self.validation_service.validate_pcap_file(resolved_path, custom_rule=custom_rule)
                validation_results.append(result)
            else:
                logger.warning(f"PCAP文件不存在: {pcap_file} (解析路径: {resolved_path})")
                # 创建一个失败的验证结果
                from .pcap_validation_service import ValidationResult, ValidationStatus
                failed_result = ValidationResult(
                    pcap_file=pcap_file,
                    status=ValidationStatus.FILE_NOT_FOUND,
                    success=False,
                    total_sql_found=0,
                    expected_sql_matches={},
                    missing_sqls=[],
                    analysis_result=None,
                    validation_time=0,
                    error_message=f"文件不存在: {resolved_path}"
                )
                validation_results.append(failed_result)
        
        # 生成任务验证报告
        task_report = self._generate_task_validation_report(task, validation_results)

        success_rate = task_report.get('summary', {}).get('success_rate', 0)
        logger.info(f"任务验证完成: {task.task_id}, 成功率: {success_rate:.1%}")

        return task_report
    
    def _create_validation_rule_for_task(self, task: CaptureTask) -> ValidationRule:
        """为任务创建自定义验证规则"""
        # 映射数据库类型
        db_type_mapping = {
            'mysql': DatabaseType.MYSQL,
            'postgresql': DatabaseType.POSTGRESQL,
            'postgres': DatabaseType.POSTGRESQL,
            'mongodb': DatabaseType.MONGODB,
            'mongo': DatabaseType.MONGODB,
            'oracle': DatabaseType.ORACLE,
            'sqlserver': DatabaseType.SQLSERVER,
            'mssql': DatabaseType.SQLSERVER,
            'gaussdb': DatabaseType.GAUSSDB
        }
        
        database_type = db_type_mapping.get(task.database_type, DatabaseType.MYSQL)
        
        # 映射SQL操作类型
        sql_type_mapping = {
            'SELECT': SQLType.SELECT,
            'INSERT': SQLType.INSERT,
            'UPDATE': SQLType.UPDATE,
            'DELETE': SQLType.DELETE,
            'CREATE': SQLType.CREATE,
            'DROP': SQLType.DROP,
            'ALTER': SQLType.ALTER,
            'GRANT': SQLType.GRANT,
            'REVOKE': SQLType.REVOKE,
            'COMMIT': SQLType.COMMIT,
            'ROLLBACK': SQLType.ROLLBACK
        }
        
        # 创建预期SQL列表
        expected_sqls = []
        for operation in task.expected_operations:
            operation_upper = operation.upper()
            if operation_upper in sql_type_mapping:
                sql_type = sql_type_mapping[operation_upper]
                
                # 根据数据库类型调整SQL模式
                if database_type == DatabaseType.MONGODB:
                    # MongoDB使用不同的操作名称
                    mongo_patterns = {
                        'SELECT': 'find',
                        'INSERT': 'insert',
                        'UPDATE': 'update',
                        'DELETE': 'delete'
                    }
                    pattern = mongo_patterns.get(operation_upper, operation_upper)
                else:
                    pattern = operation_upper
                
                expected_sqls.append(ExpectedSQL(
                    sql_pattern=pattern,
                    sql_type=sql_type,
                    database_type=database_type,
                    required=True,
                    min_occurrences=1
                ))
        
        # 如果没有指定预期操作，添加通用的查询操作
        if not expected_sqls:
            if database_type == DatabaseType.MONGODB:
                expected_sqls.append(ExpectedSQL("find", SQLType.SELECT, database_type, required=False))
            else:
                expected_sqls.append(ExpectedSQL("SELECT", SQLType.SELECT, database_type, required=False))
        
        return ValidationRule(
            name=f"task_{task.task_id}",
            description=f"任务 {task.task_id} 的验证规则",
            expected_sqls=expected_sqls,
            min_total_sql_count=1,
            min_confidence=0.5
        )
    
    def _generate_task_validation_report(self, task: CaptureTask, 
                                       validation_results: List[ValidationResult]) -> Dict[str, Any]:
        """生成任务验证报告"""
        total_files = len(validation_results)
        successful_files = sum(1 for r in validation_results if r.success)
        failed_files = total_files - successful_files
        
        total_sql_found = sum(r.total_sql_found for r in validation_results)
        files_with_sql = sum(1 for r in validation_results if r.total_sql_found > 0)
        
        # 统计预期操作的匹配情况
        operation_matches = {}
        for operation in task.expected_operations:
            operation_matches[operation] = 0
            for result in validation_results:
                if operation.upper() in result.expected_sql_matches:
                    operation_matches[operation] += result.expected_sql_matches[operation.upper()]
        
        # 计算成功率
        success_rate = successful_files / total_files if total_files > 0 else 0
        
        # 生成建议
        recommendations = []
        if success_rate < 0.5:
            recommendations.append("抓包成功率较低，建议检查网络接口配置和抓包过滤条件")
        
        if total_sql_found == 0:
            recommendations.append("未检测到任何SQL语句，可能需要调整抓包策略或检查数据库连接")
        
        missing_operations = [op for op, count in operation_matches.items() if count == 0]
        if missing_operations:
            recommendations.append(f"缺少预期的操作: {', '.join(missing_operations)}")
        
        if files_with_sql < total_files:
            recommendations.append(f"有 {total_files - files_with_sql} 个文件未包含SQL语句")
        
        report = {
            "task_id": task.task_id,
            "database_type": task.database_type,
            "expected_operations": task.expected_operations,
            "task_duration": task.end_time - task.start_time if task.end_time else 0,
            "summary": {
                "total_files": total_files,
                "successful_files": successful_files,
                "failed_files": failed_files,
                "success_rate": success_rate,
                "total_sql_found": total_sql_found,
                "files_with_sql": files_with_sql
            },
            "operation_matches": operation_matches,
            "file_results": [
                {
                    "file": os.path.basename(r.pcap_file),
                    "status": r.status.value,
                    "success": r.success,
                    "sql_count": r.total_sql_found,
                    "error": r.error_message
                }
                for r in validation_results
            ],
            "recommendations": recommendations,
            "validation_time": datetime.now().isoformat()
        }
        
        return report
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        if task_id in self.active_tasks:
            task = self.active_tasks[task_id]
            return {
                "task_id": task_id,
                "status": "active",
                "database_type": task.database_type,
                "expected_operations": task.expected_operations,
                "pcap_files_count": len(task.pcap_files),
                "start_time": task.start_time,
                "duration": time.time() - task.start_time
            }
        elif task_id in self.completed_tasks:
            task = self.completed_tasks[task_id]
            return {
                "task_id": task_id,
                "status": "completed",
                "database_type": task.database_type,
                "expected_operations": task.expected_operations,
                "pcap_files_count": len(task.pcap_files),
                "start_time": task.start_time,
                "end_time": task.end_time,
                "duration": task.end_time - task.start_time if task.end_time else 0,
                "validation_result": task.validation_result
            }
        else:
            return None
    
    def list_active_tasks(self) -> List[str]:
        """列出活跃的任务"""
        return list(self.active_tasks.keys())
    
    def list_completed_tasks(self) -> List[str]:
        """列出已完成的任务"""
        return list(self.completed_tasks.keys())
    
    def auto_discover_pcap_files(self, task_id: str, pattern: str = None) -> int:
        """自动发现并添加PCAP文件到任务"""
        if task_id not in self.active_tasks:
            logger.warning(f"任务 {task_id} 不存在或已完成")
            return 0
        
        task = self.active_tasks[task_id]
        
        # 构建搜索模式
        if pattern is None:
            # 根据数据库类型和任务ID构建默认模式
            db_type = task.database_type
            patterns = [
                f"{db_type}_*_{task_id}*.pcap",
                f"{db_type}_*.pcap",
                f"*{db_type}*.pcap",
                "*.pcap"
            ]
        else:
            patterns = [pattern]
        
        found_files = []
        for pattern in patterns:
            search_path = os.path.join(self.captures_dir, pattern)
            files = glob.glob(search_path)
            
            # 过滤已经添加的文件
            new_files = [f for f in files if f not in task.pcap_files]
            found_files.extend(new_files)
            
            if new_files:
                break  # 找到文件就停止搜索
        
        # 添加找到的文件
        for file_path in found_files:
            self.add_pcap_file_to_task(task_id, file_path)
        
        logger.info(f"任务 {task_id} 自动发现并添加了 {len(found_files)} 个PCAP文件")
        return len(found_files)
    
    def cleanup_old_tasks(self, max_age_hours: int = 24):
        """清理旧的已完成任务"""
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        tasks_to_remove = []
        for task_id, task in self.completed_tasks.items():
            if task.end_time and (current_time - task.end_time) > max_age_seconds:
                tasks_to_remove.append(task_id)
        
        for task_id in tasks_to_remove:
            del self.completed_tasks[task_id]
            logger.info(f"清理旧任务: {task_id}")
        
        return len(tasks_to_remove)

    def get_completed_tasks(self) -> Dict[str, CaptureTask]:
        """获取所有已完成的任务"""
        return self.completed_tasks.copy()

    def get_active_tasks(self) -> Dict[str, CaptureTask]:
        """获取所有活跃的任务"""
        return self.active_tasks.copy()

    def get_task_by_id(self, task_id: str) -> Optional[CaptureTask]:
        """根据ID获取任务"""
        if task_id in self.active_tasks:
            return self.active_tasks[task_id]
        elif task_id in self.completed_tasks:
            return self.completed_tasks[task_id]
        return None
