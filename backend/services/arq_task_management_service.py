"""
ARQ+Redis任务管理服务
使用ARQ+Redis作为单一任务队列解决方案，替换MySQL存储任务的方式
"""

import asyncio
import json
import logging
import time
import uuid
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal

from config.redis_config import redis_manager
from utils.timezone_utils import get_current_time, format_time

logger = logging.getLogger(__name__)


def _json_default_serializer(obj):
    """将无法直接序列化到JSON的对象进行可落库转换"""
    if isinstance(obj, (datetime,)):
        return obj.isoformat()
    if isinstance(obj, Decimal):
        try:
            return float(obj)
        except Exception:
            return str(obj)
    return str(obj)


class TaskStatus:
    """任务状态常量"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class TaskInfo:
    """任务信息数据类"""
    task_id: str
    task_type: str
    status: str
    progress: int = 0
    message: str = ""
    config_id: Optional[int] = None
    server_config_id: Optional[int] = None
    sql_query: Optional[str] = None
    mongo_query: Optional[str] = None
    natural_query: Optional[str] = None
    capture_duration: Optional[int] = None
    build_requests: Optional[List] = None
    total_requests: Optional[int] = None
    job_id: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    duration: Optional[float] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    completed_at: Optional[str] = None
    failed_at: Optional[str] = None
    # 批量测试用例执行任务特有字段
    batch_name: Optional[str] = None
    database_type: Optional[str] = None
    database_version: Optional[str] = None
    total_test_cases: Optional[int] = None
    capture_enabled: Optional[bool] = None
    timeout_per_case: Optional[int] = None
    stop_on_failure: Optional[bool] = None
    description: Optional[str] = None
    # 单个测试用例执行任务特有字段
    test_case_id: Optional[str] = None
    test_case_title: Optional[str] = None
    test_case_json: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'task_id': self.task_id or "",
            'task_type': self.task_type or "",
            'status': self.status or "",
            'progress': self.progress or 0,
            'message': self.message or "",
            'config_id': self.config_id,
            'server_config_id': self.server_config_id,
            'sql_query': self.sql_query or "",
            'mongo_query': self.mongo_query or "",
            'natural_query': self.natural_query or "",
            'capture_duration': self.capture_duration,
            'build_requests': self.build_requests,
            'total_requests': self.total_requests,
            'job_id': self.job_id or "",
            'result': self.result,
            'error': self.error or "",
            'duration': self.duration,
            'created_at': self.created_at or "",
            'updated_at': self.updated_at or "",
            'completed_at': self.completed_at or "",
            'failed_at': self.failed_at or "",
            # 批量测试用例执行任务特有字段
            'batch_name': self.batch_name or "",
            'database_type': self.database_type or "",
            'database_version': self.database_version or "",
            'total_test_cases': self.total_test_cases,
            'capture_enabled': self.capture_enabled,
            'timeout_per_case': self.timeout_per_case,
            'stop_on_failure': self.stop_on_failure,
            'description': self.description or "",
            # 单个测试用例执行任务特有字段
            'test_case_id': self.test_case_id or "",
            'test_case_title': self.test_case_title or "",
            'test_case_json': self.test_case_json or ""
        }


class ARQTaskManagementService:
    """基于ARQ+Redis的任务管理服务"""

    def __init__(self):
        self.redis = None
        self.arq_pool = None
        self._initialized = False

    async def _ensure_initialized(self):
        """确保服务已初始化"""
        if not self._initialized:
            await self.initialize()

    async def _check_connection_health(self):
        """检查连接健康状态"""
        try:
            # 检查Redis连接
            if self.redis:
                await self.redis.ping()
                logger.debug("Redis连接健康")
            else:
                logger.warning("Redis连接不存在")
                return False

            # 检查ARQ连接池
            if self.arq_pool:
                # 尝试ping测试（这是最可靠的健康检查方式）
                try:
                    await self.arq_pool.ping()
                    logger.debug("ARQ连接池ping成功")
                except Exception as ping_error:
                    logger.warning(f"ARQ连接池ping失败: {ping_error}")
                    return False

                # 额外检查连接池状态（可选）
                if hasattr(self.arq_pool, 'connection_pool'):
                    # ARQ 0.25.0+ 使用 connection_pool 属性
                    logger.debug("ARQ连接池使用connection_pool")
                elif hasattr(self.arq_pool, '_pool'):
                    # 兼容旧版本
                    pool = self.arq_pool._pool
                    if pool.closed:
                        logger.warning("ARQ连接池已关闭，需要重新初始化")
                        return False
                    logger.debug("ARQ连接池使用_pool")

                logger.debug("ARQ连接池健康")
            else:
                logger.warning("ARQ连接池不存在")
                return False

            return True
        except Exception as e:
            logger.warning(f"连接健康检查失败: {e}")
            return False

    async def _reinitialize_if_needed(self):
        """如果需要则重新初始化连接"""
        try:
            if not await self._check_connection_health():
                logger.info("检测到连接问题，重新初始化...")
                self._initialized = False
                await self.initialize()
        except Exception as e:
            logger.error(f"重新初始化失败: {e}")
            raise

    async def _submit_job_with_retry(self, task_name: str, *args, max_retries: int = 3, **kwargs):
        """带重试机制的任务提交"""
        last_exception = None

        for attempt in range(max_retries):
            try:
                # 确保连接健康
                await self._ensure_initialized()
                await self._reinitialize_if_needed()

                # 检查ARQ连接池
                if not self.arq_pool:
                    from config.redis_config import redis_manager
                    logger.info(f"ARQ连接池为空，尝试从redis_manager获取 (尝试 {attempt + 1}/{max_retries})")
                    await redis_manager.ensure_connection()
                    self.arq_pool = redis_manager.arq_pool

                # 再次检查连接池状态（通过ping测试）
                try:
                    await self.arq_pool.ping()
                    logger.debug(f"ARQ连接池ping成功 (尝试 {attempt + 1}/{max_retries})")
                except Exception as ping_error:
                    logger.warning(f"ARQ连接池ping失败，重新创建连接池 (尝试 {attempt + 1}/{max_retries}): {ping_error}")
                    from config.redis_config import redis_manager
                    await redis_manager.initialize()
                    self.arq_pool = redis_manager.arq_pool

                logger.info(f"准备提交任务到ARQ队列: {task_name} (尝试 {attempt + 1}/{max_retries})")

                # 提交任务
                job = await self.arq_pool.enqueue_job(task_name, *args, **kwargs)
                logger.info(f"任务提交成功: {task_name}")
                return job

            except Exception as e:
                last_exception = e
                logger.warning(f"任务提交失败 (尝试 {attempt + 1}/{max_retries}): {e}")

                if attempt < max_retries - 1:
                    # 等待一段时间后重试
                    import asyncio
                    wait_time = (attempt + 1) * 2  # 递增等待时间
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)

                    # 强制重新初始化连接
                    self._initialized = False
                    try:
                        await self.initialize()
                    except Exception as init_error:
                        logger.error(f"重新初始化失败: {init_error}")

        # 所有重试都失败了
        logger.error(f"任务提交最终失败，已重试 {max_retries} 次: {last_exception}")
        raise last_exception

    async def initialize(self):
        """初始化服务"""
        try:
            # 初始化Redis连接
            if not redis_manager._redis:
                await redis_manager.initialize()
            self.redis = redis_manager.redis
            self.arq_pool = redis_manager.arq_pool
            self._initialized = True
            logger.info("ARQ任务管理服务初始化成功")
        except Exception as e:
            logger.error(f"ARQ任务管理服务初始化失败: {e}")
            raise
    
    def _get_task_key(self, task_id: str) -> str:
        """获取任务在Redis中的键"""
        return f"task:{task_id}"
    
    def _get_task_list_key(self, task_type: str = None) -> str:
        """获取任务列表在Redis中的键"""
        if task_type:
            return f"task_list:{task_type}"
        return "task_list:all"
    
    def _get_task_progress_key(self, task_id: str) -> str:
        """获取任务进度在Redis中的键"""
        return f"task_progress:{task_id}"

    async def create_task(self, task_info: TaskInfo) -> str:
        """创建新任务"""
        try:
            await self._ensure_initialized()

            # 生成任务ID
            if not task_info.task_id:
                task_info.task_id = str(uuid.uuid4())

            # 设置创建时间
            current_time = format_time(get_current_time())
            task_info.created_at = current_time
            task_info.updated_at = current_time

            # 存储任务信息到Redis
            task_key = self._get_task_key(task_info.task_id)
            task_data = {}

            # 处理所有字段
            for field, value in task_info.to_dict().items():
                if value is None:
                    task_data[field] = ""
                elif isinstance(value, (list, dict)):
                    task_data[field] = json.dumps(value, default=_json_default_serializer) if value else ""
                else:
                    task_data[field] = str(value)

            # 使用Redis Hash存储任务信息
            await self.redis.hset(task_key, mapping=task_data)

            # 设置过期时间（7天）
            await self.redis.expire(task_key, 7 * 24 * 3600)

            # 添加到任务列表
            await self.redis.zadd(self._get_task_list_key(), {task_info.task_id: time.time()})
            await self.redis.zadd(self._get_task_list_key(task_info.task_type), {task_info.task_id: time.time()})

            logger.info(f"任务创建成功: {task_info.task_id}")
            return task_info.task_id

        except Exception as e:
            logger.error(f"创建任务失败: {e}")
            raise

    async def get_task_info(self, task_id: str) -> Optional[TaskInfo]:
        """获取任务信息"""
        try:
            await self._ensure_initialized()

            task_key = self._get_task_key(task_id)
            task_data = await self.redis.hgetall(task_key)

            if not task_data:
                return None

            # 将bytes转换为str
            task_data = {k.decode() if isinstance(k, bytes) else k:
                        v.decode() if isinstance(v, bytes) else v
                        for k, v in task_data.items()}

            # 处理JSON字段
            if task_data.get('build_requests') and task_data['build_requests']:
                try:
                    task_data['build_requests'] = json.loads(task_data['build_requests'])
                except (json.JSONDecodeError, TypeError):
                    task_data['build_requests'] = None

            if task_data.get('result') and task_data['result']:
                try:
                    task_data['result'] = json.loads(task_data['result'])
                except (json.JSONDecodeError, TypeError):
                    task_data['result'] = None

            # 处理数值字段的类型转换
            numeric_fields = ['progress', 'config_id', 'server_config_id', 'capture_duration', 
                            'total_requests', 'total_test_cases', 'timeout_per_case']
            for field in numeric_fields:
                if field in task_data and task_data[field] is not None and task_data[field] != "":
                    try:
                        if field in ['progress', 'config_id', 'server_config_id', 'capture_duration', 
                                   'total_requests', 'total_test_cases', 'timeout_per_case']:
                            # 整数字段
                            task_data[field] = int(float(task_data[field]))
                        else:
                            # 浮点数字段
                            task_data[field] = float(task_data[field])
                    except (ValueError, TypeError):
                        logger.warning(f"无法转换字段 {field} 的值 '{task_data[field]}' 为数值类型")
                        task_data[field] = None

            # 处理布尔字段的类型转换
            boolean_fields = ['capture_enabled', 'stop_on_failure']
            for field in boolean_fields:
                if field in task_data and task_data[field] is not None and task_data[field] != "":
                    if isinstance(task_data[field], str):
                        task_data[field] = task_data[field].lower() in ['true', '1', 'yes', 'on']

            # 处理空字符串为None
            for key, value in task_data.items():
                if value == "":
                    task_data[key] = None

            # 移除TaskInfo不支持的字段
            valid_fields = {
                'task_id', 'task_type', 'status', 'progress', 'message', 'config_id',
                'server_config_id', 'sql_query', 'mongo_query', 'natural_query',
                'capture_duration', 'build_requests', 'total_requests', 'job_id',
                'result', 'error', 'duration', 'created_at', 'updated_at',
                'completed_at', 'failed_at', 'batch_name', 'database_type',
                'database_version', 'total_test_cases', 'capture_enabled',
                'timeout_per_case', 'stop_on_failure', 'description',
                'test_case_id', 'test_case_title', 'test_case_json'
            }

            # 过滤掉不支持的字段
            filtered_data = {k: v for k, v in task_data.items() if k in valid_fields}

            return TaskInfo(**filtered_data)

        except Exception as e:
            logger.error(f"获取任务信息失败: {e}")
            return None

    async def update_task(self, task_id: str, **kwargs) -> bool:
        """更新任务信息"""
        try:
            await self._ensure_initialized()

            task_key = self._get_task_key(task_id)

            # 添加更新时间
            kwargs['updated_at'] = format_time(get_current_time())

            # 处理复杂类型
            update_data = {}
            for field, value in kwargs.items():
                if field in ['build_requests', 'result'] and value is not None:
                    # JSON字段需要序列化（带默认处理，兼容Decimal/datetime等）
                    if not isinstance(value, str):
                        value = json.dumps(value, default=_json_default_serializer)
                update_data[field] = str(value) if value is not None else ""

            if not update_data:
                return True

            await self.redis.hset(task_key, mapping=update_data)

            # 检查是否有任务被更新
            task_exists = await self.redis.exists(task_key)
            success = bool(task_exists)

            if success:
                logger.info(f"任务更新成功: {task_id}")
            else:
                logger.warning(f"任务更新失败，任务不存在: {task_id}")

            return success

        except Exception as e:
            logger.error(f"更新任务失败: {e}")
            return False

    async def delete_task(self, task_id: str) -> bool:
        """删除任务"""
        try:
            await self._ensure_initialized()
            
            # 获取任务信息以确定类型
            task_info = await self.get_task_info(task_id)
            if not task_info:
                return False
            
            # 删除任务数据
            task_key = self._get_task_key(task_id)
            await self.redis.delete(task_key)
            
            # 从任务列表中移除
            await self.redis.zrem(self._get_task_list_key(), task_id)
            await self.redis.zrem(self._get_task_list_key(task_info.task_type), task_id)
            
            # 删除进度信息
            progress_key = self._get_task_progress_key(task_id)
            await self.redis.delete(progress_key)
            
            logger.info(f"任务删除成功: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除任务失败: {e}")
            return False

    async def get_task_list(self, page: int = 1, page_size: int = 20,
                           task_type: Optional[str] = None) -> Tuple[List[TaskInfo], int]:
        """获取任务列表（支持分页）"""
        try:
            await self._ensure_initialized()

            # 获取任务列表键
            list_key = self._get_task_list_key(task_type)

            # 获取总数
            total = await self.redis.zcard(list_key)

            # 获取分页数据
            offset = (page - 1) * page_size
            task_ids = await self.redis.zrevrange(list_key, offset, offset + page_size - 1)

            tasks = []
            for task_id in task_ids:
                if isinstance(task_id, bytes):
                    task_id = task_id.decode()

                task_info = await self.get_task_info(task_id)
                if task_info:
                    tasks.append(task_info)

            return tasks, total

        except Exception as e:
            logger.error(f"获取任务列表失败: {e}")
            return [], 0

    async def get_task_count(self, task_type: str = None) -> int:
        """获取任务数量"""
        try:
            await self._ensure_initialized()

            list_key = self._get_task_list_key(task_type)
            count = await self.redis.zcard(list_key)
            return count

        except Exception as e:
            logger.error(f"获取任务数量失败: {e}")
            return 0

    async def update_task_progress(self, task_id: str, *args, **kwargs):
        """更新任务进度（兼容不同调用方式）

        兼容两种调用：
        - update_task_progress(task_id, progress:int, message:str = '', status:str = None)
        - update_task_progress(task_id, status:str, progress:int, message:str)
        """
        try:
            await self._ensure_initialized()

            status = kwargs.get('status')
            progress = None
            message = kwargs.get('message', '')

            # 解析位置参数兼容
            if len(args) == 1:
                # 仅提供progress
                progress = args[0]
            elif len(args) >= 2:
                # 可能是 (status, progress, message)
                if isinstance(args[0], str) and args[0] in [
                    TaskStatus.PENDING, TaskStatus.RUNNING, TaskStatus.COMPLETED,
                    TaskStatus.FAILED, TaskStatus.CANCELLED
                ]:
                    status = args[0]
                    progress = args[1] if len(args) >= 2 else None
                    message = args[2] if len(args) >= 3 else message
                else:
                    # (progress, message)
                    progress = args[0]
                    message = args[1] if len(args) >= 2 else message

            update_data = {}
            if progress is not None:
                update_data['progress'] = progress
            if message is not None:
                update_data['message'] = message
            if status is not None:
                update_data['status'] = status

            if update_data:
                await self.update_task(task_id, **update_data)

        except Exception as e:
            logger.error(f"更新任务进度失败: {e}")

    async def get_task_progress(self, task_id: str) -> Dict[str, Any]:
        """获取任务进度"""
        try:
            await self._ensure_initialized()

            progress_key = self._get_task_progress_key(task_id)
            progress_data = await self.redis.hgetall(progress_key)

            if not progress_data:
                # 如果没有进度信息，从任务信息中获取
                task_info = await self.get_task(task_id)
                if task_info:
                    return {
                        'progress': task_info.progress,
                        'message': task_info.message,
                        'updated_at': task_info.updated_at or task_info.created_at
                    }
                return {'progress': 0, 'message': '', 'updated_at': ''}

            # 将bytes转换为str
            return {k.decode() if isinstance(k, bytes) else k:
                   v.decode() if isinstance(v, bytes) else v
                   for k, v in progress_data.items()}

        except Exception as e:
            logger.error(f"获取任务进度失败: {e}")
            return {'progress': 0, 'message': '', 'updated_at': ''}

    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            await self._ensure_initialized()

            # 清理任务相关的tcpdump进程
            try:
                from services.tcpdump_process_manager import tcpdump_manager
                await tcpdump_manager.cleanup_task_processes(task_id, "任务取消")
            except Exception as e:
                logger.warning(f"清理任务 {task_id} 的tcpdump进程失败: {e}")

            # 更新任务状态
            success = await self.update_task(task_id, status=TaskStatus.CANCELLED)

            if success:
                logger.info(f"任务已取消: {task_id}")

            return success

        except Exception as e:
            logger.error(f"取消任务失败: {e}")
            return False

    async def cleanup_expired_tasks(self, days: int = 7) -> int:
        """清理过期任务"""
        try:
            await self._ensure_initialized()

            cutoff_time = time.time() - (days * 24 * 3600)
            cleaned_count = 0

            # 清理所有任务列表中的过期任务
            for list_key in [self._get_task_list_key(), self._get_task_list_key("*")]:
                # 获取过期的任务ID
                expired_task_ids = await self.redis.zrangebyscore(list_key, 0, cutoff_time)

                for task_id in expired_task_ids:
                    if isinstance(task_id, bytes):
                        task_id = task_id.decode()

                    # 删除任务
                    if await self.delete_task(task_id):
                        cleaned_count += 1

            logger.info(f"清理了 {cleaned_count} 个过期任务")
            return cleaned_count

        except Exception as e:
            logger.error(f"清理过期任务失败: {e}")
            return 0

    # ==================== 任务状态管理方法 ====================

    async def update_task_result(self, task_id: str, status: str, progress: int, message: str, result: Dict[str, Any]):
        """更新任务最终结果与状态"""
        try:
            await self._ensure_initialized()
            update_data = {
                'status': status,
                'progress': progress,
                'message': message,
                'result': result,
                'updated_at': format_time(get_current_time()),
                'completed_at': format_time(get_current_time()) if status == TaskStatus.COMPLETED else None,
                'failed_at': format_time(get_current_time()) if status == TaskStatus.FAILED else None
            }
            await self.update_task(task_id, **update_data)
        except Exception as e:
            logger.error(f"更新任务结果失败: {e}")

    async def set_task_completed(self, task_id: str, result: Dict[str, Any], duration: float = None):
        """设置任务完成"""
        try:
            update_data = {
                'status': TaskStatus.COMPLETED,
                'progress': 100,
                'result': result,
                'completed_at': format_time(get_current_time())
            }

            # 只有当结果中包含消息时才更新消息，否则保持当前消息不变
            if isinstance(result, dict) and 'message' in result:
                update_data['message'] = result['message']
            # 不设置默认消息，保持当前的消息不变

            if duration is not None:
                update_data['duration'] = duration

            await self.update_task(task_id, **update_data)

        except Exception as e:
            logger.error(f"设置任务完成失败: {e}")

    async def set_task_failed(self, task_id: str, error: str, duration: float = None):
        """设置任务失败"""
        try:
            update_data = {
                'status': TaskStatus.FAILED,
                'progress': 0,
                'message': f"任务失败: {error}",
                'error': error,
                'failed_at': format_time(get_current_time())
            }

            if duration is not None:
                update_data['duration'] = duration

            await self.update_task(task_id, **update_data)

        except Exception as e:
            logger.error(f"设置任务失败失败: {e}")

    async def cancel_task(self, task_id: str) -> bool:
        """取消并删除任务"""
        try:
            await self._ensure_initialized()

            # 写入取消标记，供任务侧及时感知并自我中断
            try:
                await self.redis.set(f"task_cancelled:{task_id}", "1", ex=24 * 3600)
            except Exception as e:
                logger.warning(f"写入取消标记失败: {e}")

            # 获取任务信息
            task_info = await self.get_task_info(task_id)
            if not task_info:
                return False

            # 清理任务相关的tcpdump进程
            try:
                from services.tcpdump_process_manager import tcpdump_manager
                await tcpdump_manager.cleanup_task_processes(task_id, "任务取消")
            except Exception as e:
                logger.warning(f"清理任务 {task_id} 的tcpdump进程失败: {e}")

            # 尝试取消ARQ任务
            if self.arq_pool and task_info.job_id:
                try:
                    # ARQ 0.25.0版本的Job导入路径
                    from arq.jobs import Job
                    job = Job(task_info.job_id, self.arq_pool)
                    await job.abort()
                except ImportError:
                    try:
                        # 尝试旧版本的导入路径
                        from arq import Job
                        job = Job(task_info.job_id, self.arq_pool)
                        await job.abort()
                    except Exception as e:
                        logger.warning(f"取消ARQ任务失败(旧版本): {e}")
                except Exception as e:
                    logger.warning(f"取消ARQ任务失败: {e}")

            # 删除Redis中与该任务相关的所有数据
            try:
                # 删除任务主数据
                task_key = self._get_task_key(task_id)
                await self.redis.delete(task_key)
                
                # 从任务列表中移除
                await self.redis.zrem(self._get_task_list_key(), task_id)
                await self.redis.zrem(self._get_task_list_key(task_info.task_type), task_id)
                
                # 删除进度信息
                progress_key = self._get_task_progress_key(task_id)
                await self.redis.delete(progress_key)
                
                # 删除取消标记
                await self.redis.delete(f"task_cancelled:{task_id}")
                
                # 删除可能存在的ARQ相关键
                arq_keys_to_delete = [
                    f"arq:job:{task_info.job_id if task_info.job_id else task_id}",
                    f"arq:result:{task_info.job_id if task_info.job_id else task_id}",
                    f"arq:in-progress:{task_info.job_id if task_info.job_id else task_id}",
                ]
                
                for key in arq_keys_to_delete:
                    try:
                        await self.redis.delete(key)
                    except Exception as e:
                        logger.debug(f"删除ARQ键 {key} 失败: {e}")
                
                logger.info(f"任务已取消并删除: {task_id}")
                return True
                
            except Exception as e:
                logger.error(f"删除任务数据失败: {e}")
                # 即使删除失败，也返回True，因为至少取消了任务
                return True

        except Exception as e:
            logger.error(f"取消任务失败: {e}")
            return False

    async def update_task_sql(self, task_id: str, sql_query: str) -> bool:
        """更新任务的SQL查询（用于AI任务在生成SQL后更新）"""
        try:
            return await self.update_task(task_id, sql_query=sql_query)
        except Exception as e:
            logger.error(f"更新任务SQL失败: {e}")
            return False

    async def get_all_tasks(self) -> List[Dict[str, Any]]:
        """获取所有任务（用于ARQ任务日志接口）"""
        try:
            await self._ensure_initialized()

            # 获取所有任务列表
            all_task_keys = []
            async for key in self.redis.scan_iter(match="task:*"):
                if isinstance(key, bytes):
                    key = key.decode()
                # 提取任务ID
                task_id = key.replace("task:", "")
                all_task_keys.append(task_id)

            tasks = []
            for task_id in all_task_keys:
                try:
                    # 直接从Redis获取原始数据
                    task_key = self._get_task_key(task_id)
                    task_data = await self.redis.hgetall(task_key)

                    if not task_data:
                        continue

                    # 转换bytes为字符串
                    task_dict = {}
                    for k, v in task_data.items():
                        if isinstance(k, bytes):
                            k = k.decode()
                        if isinstance(v, bytes):
                            v = v.decode()
                        task_dict[k] = v

                    # 确保必要字段存在
                    if 'task_id' not in task_dict:
                        task_dict['task_id'] = task_id
                    if 'task_type' not in task_dict:
                        task_dict['task_type'] = 'unknown'
                    if 'status' not in task_dict:
                        task_dict['status'] = 'unknown'

                    # 处理progress字段，确保是数值
                    if 'progress' in task_dict:
                        try:
                            task_dict['progress'] = int(float(task_dict['progress']))
                        except (ValueError, TypeError):
                            task_dict['progress'] = 0
                    else:
                        task_dict['progress'] = 0

                    tasks.append(task_dict)

                except Exception as task_error:
                    logger.warning(f"处理任务 {task_id} 时出错: {task_error}")
                    continue

            # 按创建时间倒序排序
            tasks.sort(key=lambda x: x.get('created_at', ''), reverse=True)
            return tasks

        except Exception as e:
            logger.error(f"获取所有任务失败: {e}")
            return []

    async def cleanup_expired_tasks(self, task_type: Optional[str] = None, status: Optional[str] = None, hours: float = 24):
        """清理过期任务

        Args:
            task_type: 任务类型，如果指定则只清理该类型的任务
            status: 任务状态，如果指定则只清理该状态的任务
            hours: 过期时间（小时），默认24小时
        """
        try:
            await self._ensure_initialized()

            # 计算过期时间
            cutoff_time = time.time() - (hours * 3600)
            deleted_count = 0

            # 获取所有任务列表键
            list_keys = [self._get_task_list_key()]
            if task_type:
                list_keys = [self._get_task_list_key(task_type)]
            else:
                # 获取所有类型的任务列表
                pattern = f"task_list:*"
                all_keys = await self.redis.keys(pattern)
                list_keys.extend([key.decode() if isinstance(key, bytes) else key for key in all_keys])

            for list_key in list_keys:
                # 获取过期的任务ID
                expired_task_ids = await self.redis.zrangebyscore(list_key, 0, cutoff_time)

                for task_id in expired_task_ids:
                    if isinstance(task_id, bytes):
                        task_id = task_id.decode()

                    # 检查任务状态（如果指定了状态过滤）
                    if status:
                        task_info = await self.get_task_info(task_id)
                        if not task_info or task_info.status != status:
                            continue

                    # 删除任务
                    if await self.delete_task(task_id):
                        deleted_count += 1

            # 构建日志消息
            message_parts = []
            if task_type:
                message_parts.append(f"{task_type} 类型")
            if status:
                status_names = {
                    "failed": "失败",
                    "cancelled": "已取消"
                }
                message_parts.append(f"{status_names.get(status, status)} 状态")

            if message_parts:
                logger.info(f"清理了 {deleted_count} 个过期的 {' '.join(message_parts)} 任务")
            else:
                logger.info(f"清理了 {deleted_count} 个过期任务")

            return deleted_count

        except Exception as e:
            logger.error(f"清理过期任务失败: {e}")
            return 0

    # ==================== 任务提交方法 ====================

    async def submit_mysql_capture_task(self, config_id: int, sql_query: str, capture_duration: int = 30) -> str:
        """提交MySQL抓包任务"""
        try:
            await self._ensure_initialized()

            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 使用重试机制提交任务到ARQ队列
            job = await self._submit_job_with_retry(
                'mysql_capture_task',
                config_id,
                sql_query,
                capture_duration,
                _job_id=task_id,
                _queue_name='capture_tasks'
            )

            # 创建任务记录
            task_info = TaskInfo(
                task_id=task_id,
                task_type="mysql_capture",
                status=TaskStatus.PENDING,
                config_id=config_id,
                sql_query=sql_query,
                capture_duration=capture_duration,
                job_id=job.job_id if job else task_id
            )

            await self.create_task(task_info)
            logger.info(f"MySQL抓包任务已提交: {task_id}")
            return task_id

        except Exception as e:
            logger.error(f"提交MySQL抓包任务失败: {e}")
            raise

    async def submit_postgres_capture_task(self, config_id: int, sql_query: str, capture_duration: int = 30) -> str:
        """提交PostgreSQL抓包任务"""
        try:
            await self._ensure_initialized()

            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 提交任务到ARQ队列
            job = await self.arq_pool.enqueue_job(
                'postgres_capture_task',
                config_id,
                sql_query,
                capture_duration,
                _job_id=task_id,
                _queue_name='capture_tasks'
            )

            # 创建任务记录
            task_info = TaskInfo(
                task_id=task_id,
                task_type="postgres_capture",
                status=TaskStatus.PENDING,
                config_id=config_id,
                sql_query=sql_query,
                capture_duration=capture_duration,
                job_id=job.job_id if job else task_id
            )

            await self.create_task(task_info)
            logger.info(f"PostgreSQL抓包任务已提交: {task_id}")
            return task_id

        except Exception as e:
            logger.error(f"提交PostgreSQL抓包任务失败: {e}")
            raise

    async def submit_mongo_capture_task(self, config_id: int, mongo_query: str, capture_duration: int = 30, executor_type: str = "python") -> str:
        """提交MongoDB抓包任务"""
        try:
            await self._ensure_initialized()

            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 提交任务到ARQ队列
            job = await self.arq_pool.enqueue_job(
                'mongo_capture_task',
                config_id,
                mongo_query,
                capture_duration,
                executor_type,
                _job_id=task_id,
                _queue_name='capture_tasks'
            )

            # 创建任务记录
            task_info = TaskInfo(
                task_id=task_id,
                task_type="mongo_capture",
                status=TaskStatus.PENDING,
                config_id=config_id,
                mongo_query=mongo_query,
                capture_duration=capture_duration,
                job_id=job.job_id if job else task_id
            )

            await self.create_task(task_info)
            logger.info(f"MongoDB抓包任务已提交: {task_id}")
            return task_id

        except Exception as e:
            logger.error(f"提交MongoDB抓包任务失败: {e}")
            raise

    async def submit_docker_build_task(self, server_config_id: int, build_requests: list) -> str:
        """提交Docker环境构建任务"""
        try:
            await self._ensure_initialized()

            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 提交任务到ARQ队列
            job = await self.arq_pool.enqueue_job(
                'docker_build_task',
                server_config_id,
                build_requests,
                _job_id=task_id,
                _queue_name='capture_tasks'
            )

            # 创建任务记录
            task_info = TaskInfo(
                task_id=task_id,
                task_type="docker_build",
                status=TaskStatus.PENDING,
                server_config_id=server_config_id,
                build_requests=build_requests,
                total_requests=len(build_requests),
                job_id=job.job_id if job else task_id
            )

            await self.create_task(task_info)
            logger.info(f"Docker构建任务已提交: {task_id}")
            return task_id

        except Exception as e:
            logger.error(f"提交Docker构建任务失败: {e}")
            raise

    async def submit_ai_mysql_capture_task(self, config_id: int, natural_query: str, capture_duration: int = 30) -> str:
        """提交AI+MySQL抓包任务"""
        try:
            await self._ensure_initialized()

            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 提交任务到ARQ队列
            job = await self.arq_pool.enqueue_job(
                'ai_mysql_capture_task',
                config_id,
                natural_query,
                capture_duration,
                _job_id=task_id,
                _queue_name='capture_tasks'
            )

            # 创建任务记录
            task_info = TaskInfo(
                task_id=task_id,
                task_type="ai_mysql_capture",
                status=TaskStatus.PENDING,
                config_id=config_id,
                natural_query=natural_query,
                capture_duration=capture_duration,
                job_id=job.job_id if job else task_id
            )

            await self.create_task(task_info)
            logger.info(f"AI+MySQL抓包任务已提交: {task_id}")
            return task_id

        except Exception as e:
            logger.error(f"提交AI+MySQL抓包任务失败: {e}")
            raise

    async def submit_ai_postgres_capture_task(self, config_id: int, natural_query: str, capture_duration: int = 30) -> str:
        """提交AI+PostgreSQL抓包任务"""
        try:
            await self._ensure_initialized()

            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 提交任务到ARQ队列
            job = await self.arq_pool.enqueue_job(
                'ai_postgres_capture_task',
                config_id,
                natural_query,
                capture_duration,
                _job_id=task_id,
                _queue_name='capture_tasks'
            )

            # 创建任务记录
            task_info = TaskInfo(
                task_id=task_id,
                task_type="ai_postgres_capture",
                status=TaskStatus.PENDING,
                config_id=config_id,
                natural_query=natural_query,
                capture_duration=capture_duration,
                job_id=job.job_id if job else task_id
            )

            await self.create_task(task_info)
            logger.info(f"AI+PostgreSQL抓包任务已提交: {task_id}")
            return task_id

        except Exception as e:
            logger.error(f"提交AI+PostgreSQL抓包任务失败: {e}")
            raise

    async def submit_ai_mongo_capture_task(self, config_id: int, natural_query: str, capture_duration: int = 30, executor_type: str = "python") -> str:
        """提交AI+MongoDB抓包任务"""
        try:
            await self._ensure_initialized()

            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 提交任务到ARQ队列
            job = await self.arq_pool.enqueue_job(
                'ai_mongo_capture_task',
                config_id,
                natural_query,
                capture_duration,
                executor_type,
                _job_id=task_id,
                _queue_name='capture_tasks'
            )

            # 创建任务记录
            task_info = TaskInfo(
                task_id=task_id,
                task_type="ai_mongo_capture",
                status=TaskStatus.PENDING,
                config_id=config_id,
                natural_query=natural_query,
                capture_duration=capture_duration,
                job_id=job.job_id if job else task_id
            )

            await self.create_task(task_info)
            logger.info(f"AI+MongoDB抓包任务已提交: {task_id}")
            return task_id

        except Exception as e:
            logger.error(f"提交AI+MongoDB抓包任务失败: {e}")
            raise

    async def submit_gaussdb_capture_task(self, config_id: int, sql_query: str, capture_duration: int = 30) -> str:
        """提交GaussDB抓包任务"""
        try:
            await self._ensure_initialized()

            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 提交任务到ARQ队列
            job = await self.arq_pool.enqueue_job(
                'gaussdb_capture_task',
                config_id,
                sql_query,
                capture_duration,
                _job_id=task_id,
                _queue_name='capture_tasks'
            )

            # 创建任务记录
            task_info = TaskInfo(
                task_id=task_id,
                task_type="gaussdb_capture",
                status=TaskStatus.PENDING,
                config_id=config_id,
                sql_query=sql_query,
                capture_duration=capture_duration,
                job_id=job.job_id if job else task_id
            )

            await self.create_task(task_info)
            logger.info(f"GaussDB抓包任务已提交: {task_id}")
            return task_id

        except Exception as e:
            logger.error(f"提交GaussDB抓包任务失败: {e}")
            raise

    async def submit_ai_gaussdb_capture_task(self, config_id: int, natural_query: str, capture_duration: int = 30) -> str:
        """提交AI+GaussDB抓包任务"""
        try:
            await self._ensure_initialized()

            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 提交任务到ARQ队列
            job = await self.arq_pool.enqueue_job(
                'ai_gaussdb_capture_task',
                config_id,
                natural_query,
                capture_duration,
                _job_id=task_id,
                _queue_name='capture_tasks'
            )

            # 创建任务记录
            task_info = TaskInfo(
                task_id=task_id,
                task_type="ai_gaussdb_capture",
                status=TaskStatus.PENDING,
                config_id=config_id,
                natural_query=natural_query,
                capture_duration=capture_duration,
                job_id=job.job_id if job else task_id
            )

            await self.create_task(task_info)
            logger.info(f"AI+GaussDB抓包任务已提交: {task_id}")
            return task_id

        except Exception as e:
            logger.error(f"提交AI+GaussDB抓包任务失败: {e}")
            raise

    async def submit_oracle_capture_task(self, config_id: int, sql_query: str, capture_duration: int = 30) -> str:
        """提交Oracle抓包任务"""
        try:
            await self._ensure_initialized()

            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 提交任务到ARQ队列
            job = await self.arq_pool.enqueue_job(
                'oracle_capture_task',
                config_id,
                sql_query,
                capture_duration,
                _job_id=task_id,
                _queue_name='capture_tasks'
            )

            # 创建任务记录
            task_info = TaskInfo(
                task_id=task_id,
                task_type="oracle_capture",
                status=TaskStatus.PENDING,
                config_id=config_id,
                sql_query=sql_query,
                capture_duration=capture_duration,
                job_id=job.job_id if job else task_id
            )

            await self.create_task(task_info)
            logger.info(f"Oracle抓包任务已提交: {task_id}")
            return task_id

        except Exception as e:
            logger.error(f"提交Oracle抓包任务失败: {e}")
            raise

    async def submit_ai_oracle_capture_task(self, config_id: int, natural_query: str, capture_duration: int = 30) -> str:
        """提交AI+Oracle抓包任务"""
        try:
            await self._ensure_initialized()

            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 提交任务到ARQ队列
            job = await self.arq_pool.enqueue_job(
                'ai_oracle_capture_task',
                config_id,
                natural_query,
                capture_duration,
                _job_id=task_id,
                _queue_name='capture_tasks'
            )

            # 创建任务记录
            task_info = TaskInfo(
                task_id=task_id,
                task_type="ai_oracle_capture",
                status=TaskStatus.PENDING,
                config_id=config_id,
                natural_query=natural_query,
                capture_duration=capture_duration,
                job_id=job.job_id if job else task_id
            )

            await self.create_task(task_info)
            logger.info(f"AI+Oracle抓包任务已提交: {task_id}")
            return task_id

        except Exception as e:
            logger.error(f"提交AI+Oracle抓包任务失败: {e}")
            raise

    async def submit_ai_test_case_generation_task(self, requirement: str, database_type: str = "mysql", database_version: str = "", operation_type: str = "查询", batch_size: int = 1) -> str:
        """提交AI测试用例生成任务"""
        try:
            await self._ensure_initialized()

            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 提交任务到ARQ队列
            job = await self.arq_pool.enqueue_job(
                'ai_test_case_generation_task',
                requirement,
                database_type,
                database_version,
                operation_type,
                batch_size,
                _job_id=task_id,
                _queue_name='capture_tasks'
            )

            # 创建任务记录
            task_info = TaskInfo(
                task_id=task_id,
                task_type="ai_test_case_generation",
                status=TaskStatus.PENDING,
                message=f"生成{batch_size}个{database_type}数据库的{operation_type}测试用例",
                natural_query=requirement,
                job_id=job.job_id if job else task_id
            )

            await self.create_task(task_info)
            logger.info(f"AI测试用例生成任务已提交: {task_id}")
            return task_id

        except Exception as e:
            logger.error(f"提交AI测试用例生成任务失败: {e}")
            raise

    async def submit_docker_image_build_task(self, server_config_id: int, image_repository: str,
                                           image_tag: str, database_type: str, ai_prompt: str) -> str:
        """提交Docker镜像构建任务"""
        try:
            await self._ensure_initialized()

            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 提交任务到ARQ队列
            job = await self.arq_pool.enqueue_job(
                'docker_image_build_task',
                server_config_id,
                image_repository,
                image_tag,
                database_type,
                ai_prompt,
                _job_id=task_id,
                _queue_name='capture_tasks'
            )

            # 创建任务记录
            task_info = TaskInfo(
                task_id=task_id,
                task_type="docker_image_build",
                status=TaskStatus.PENDING,
                server_config_id=server_config_id,
                build_requests=[{
                    "image_repository": image_repository,
                    "image_tag": image_tag,
                    "database_type": database_type,
                    "ai_prompt": ai_prompt
                }],
                total_requests=1,
                job_id=job.job_id if job else task_id
            )

            await self.create_task(task_info)
            logger.info(f"Docker镜像构建任务已提交: {task_id}")
            return task_id

        except Exception as e:
            logger.error(f"提交Docker镜像构建任务失败: {e}")
            raise

    async def submit_gateway_single_execution_task(self, test_case_id: str, gateway_server_id: int, wait_time: int = 2) -> str:
        """提交网关单个执行任务（ARQ）"""
        try:
            await self._ensure_initialized()

            task_id = str(uuid.uuid4())

            job = await self._submit_job_with_retry(
                'gateway_single_execution_task',
                test_case_id,
                gateway_server_id,
                wait_time,
                _job_id=task_id,
                _queue_name='capture_tasks'
            )

            task_info = TaskInfo(
                task_id=task_id,
                task_type="网关单个执行",
                status=TaskStatus.PENDING,
                progress=0,
                message="网关单个执行任务已提交",
                config_id=gateway_server_id,
                test_case_id=test_case_id,
                job_id=job.job_id if job else task_id
            )

            await self.create_task(task_info)
            logger.info(f"网关单个执行任务已提交: {task_id}")
            return task_id

        except Exception as e:
            logger.error(f"提交网关单个执行任务失败: {e}")
            raise

    async def submit_gateway_batch_execution_task(self, test_case_ids: List[str], gateway_server_id: int, wait_time: int = 2) -> str:
        """提交网关批量执行任务（ARQ）"""
        try:
            await self._ensure_initialized()

            task_id = str(uuid.uuid4())

            job = await self._submit_job_with_retry(
                'gateway_batch_execution_task',
                test_case_ids,
                gateway_server_id,
                wait_time,
                _job_id=task_id,
                _queue_name='capture_tasks'
            )

            task_info = TaskInfo(
                task_id=task_id,
                task_type="网关批量执行",
                status=TaskStatus.PENDING,
                progress=0,
                message=f"准备执行 {len(test_case_ids)} 个用例...",
                config_id=gateway_server_id,
                total_requests=len(test_case_ids),
                job_id=job.job_id if job else task_id,
                description="网关批量执行"
            )

            await self.create_task(task_info)
            logger.info(f"网关批量执行任务已提交: {task_id}")
            return task_id

        except Exception as e:
            logger.error(f"提交网关批量执行任务失败: {e}")
            raise

    async def submit_batch_test_case_execution_task(
        self,
        batch_name: str,
        database_type: str,
        database_version: str,
        config_id: int,
        test_case_items: List[Dict[str, Any]],
        capture_enabled: bool = True,
        timeout_per_case: int = 300,
        stop_on_failure: bool = False,
        use_c_executor: bool = False,
        description: str = None
    ) -> str:
        """提交批量测试用例执行任务"""
        try:
            await self._ensure_initialized()

            # 检查并重新初始化连接（如果需要）
            await self._reinitialize_if_needed()

            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 使用重试机制提交任务到ARQ队列
            job = await self._submit_job_with_retry(
                'batch_test_case_execution_task',
                batch_name,
                database_type,
                database_version,
                config_id,
                test_case_items,
                capture_enabled,
                timeout_per_case,
                stop_on_failure,
                use_c_executor,
                _job_id=task_id,
                _queue_name='capture_tasks'
            )

            # 创建任务记录
            task_info = TaskInfo(
                task_id=task_id,
                task_type="batch_test_case_execution",
                status="pending",
                progress=0,
                message="批量测试用例执行任务已提交",
                config_id=config_id,
                sql_query=None,
                mongo_query=None,
                natural_query=f"批量执行: {batch_name}",
                capture_duration=None,
                result=None,
                error=None,
                duration=0,
                server_config_id=None,
                build_requests=None,
                total_requests=len(test_case_items),
                job_id=job.job_id if job else task_id,
                # 批量执行任务特有字段
                batch_name=batch_name,
                database_type=database_type,
                database_version=database_version,
                total_test_cases=len(test_case_items),
                capture_enabled=capture_enabled,
                timeout_per_case=timeout_per_case,
                stop_on_failure=stop_on_failure,
                description=description
            )

            await self.create_task(task_info)
            logger.info(f"批量测试用例执行任务已提交: {task_id}")
            return task_id

        except Exception as e:
            logger.error(f"提交批量测试用例执行任务失败: {e}")
            raise

    async def submit_single_test_case_execution_task(
        self,
        test_case_json: str,
        config_id: int,
        capture_enabled: bool = True,
        test_case_id: Optional[str] = None,
        test_case_title: Optional[str] = None,
        use_c_executor: bool = False
    ) -> str:
        """提交单个测试用例执行任务"""
        try:
            await self._ensure_initialized()

            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 提交任务到ARQ队列
            job = await self.arq_pool.enqueue_job(
                'single_test_case_execution_task',
                test_case_json,
                config_id,
                capture_enabled,
                test_case_id,
                use_c_executor,
                _job_id=task_id,
                _queue_name='capture_tasks'
            )

            # 生成任务描述
            description = f"执行测试用例: {test_case_title}" if test_case_title else "单个测试用例执行"

            # 创建任务记录
            task_info = TaskInfo(
                task_id=task_id,
                task_type="single_test_case_execution",
                status=TaskStatus.PENDING,
                config_id=config_id,
                test_case_id=test_case_id,
                test_case_title=test_case_title,
                test_case_json=test_case_json,
                capture_enabled=capture_enabled,
                description=description,
                job_id=job.job_id if job else task_id
            )

            await self.create_task(task_info)
            logger.info(f"单个测试用例执行任务已提交: {task_id}")
            return task_id

        except Exception as e:
            logger.error(f"提交单个测试用例执行任务失败: {e}")
            raise


# 创建全局实例
arq_task_management_service = ARQTaskManagementService()
