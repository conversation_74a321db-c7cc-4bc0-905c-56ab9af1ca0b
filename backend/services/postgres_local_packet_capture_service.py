"""
PostgreSQL数据包捕获服务 - 本地抓包
"""

import asyncio
import logging
import os
from typing import Optional, Dict, Any
from datetime import datetime

from services.local_tcpdump_service import LocalTcpdumpService
from utils.local_network_utils import LocalNetworkUtils
from services.capture_file_service import capture_file_service
from models.capture_file import CaptureFileCreate

logger = logging.getLogger(__name__)

class PostgresLocalPacketCaptureService:
    """PostgreSQL数据包捕获服务 - 本地抓包"""

    def __init__(self):
        self.is_capturing = False
        self.current_file: Optional[str] = None
        self.postgres_port = 5432  # PostgreSQL默认端口
        self.capture_dir = "captures"  # 本地抓包文件目录

        # 确保本地captures目录存在
        os.makedirs(self.capture_dir, exist_ok=True)

        # 本地tcpdump服务
        self.local_tcpdump_service = LocalTcpdumpService(self.capture_dir)

    async def start_local_capture(self, postgres_host: str = "**************", postgres_port: int = 5432, *, step_identifier: str | None = None) -> str:
        """
        启动本地PostgreSQL数据包捕获
        直接监控指定主机和端口的流量

        Args:
            postgres_host: PostgreSQL主机地址
            postgres_port: PostgreSQL端口

        Returns:
            str: 抓包文件路径
        """
        logger.info(f"Starting PostgreSQL packet capture - Target server: {postgres_host}:{postgres_port}")

        try:
            # 保存当前抓包配置，用于后续保存到数据库
            self._current_target_host = postgres_host
            self._current_target_port = postgres_port

            # 获取最佳本地网络接口
            interface = LocalNetworkUtils.get_best_local_interface()
            logger.info(f"Selected local interface: {interface}")

            # 构建过滤表达式 - 监控指定主机和端口的流量
            filter_expr = f"host {postgres_host} and tcp port {postgres_port}"
            logger.info(f"Monitoring traffic: {filter_expr}")

            # 启动本地tcpdump（带步骤标识以输出更可追踪的文件名）
            capture_file = await self.local_tcpdump_service.start_capture(
                database_type="postgres",
                target_port=postgres_port,
                interface=interface,
                filter_expression=filter_expr,
                step_identifier=step_identifier
            )

            self.current_file = capture_file
            self.is_capturing = True

            logger.info(f"PostgreSQL packet capture started successfully: {capture_file}")
            logger.info(f"Monitoring traffic to/from {postgres_host}:{postgres_port}")
            return capture_file

        except Exception as e:
            logger.error(f"Failed to start PostgreSQL local capture: {str(e)}")
            raise

    async def start_capture(self, target_host: str = None, target_port: int = None, server_config_id: int = None, *, step_identifier: str | None = None) -> str:
        """启动PostgreSQL数据包捕获"""
        postgres_host = target_host or "**************"  # 默认使用远程PostgreSQL服务器
        postgres_port = target_port or 5432

        logger.info(f"PostgreSQL抓包配置 - Host: {postgres_host}, Port: {postgres_port}")

        # 如果检测到抓包状态异常，强制重置
        if self.is_capturing:
            logger.warning("检测到PostgreSQL抓包状态异常，强制重置状态")
            await self.stop_capture()

        # 启动抓包
        try:
            logger.info(f"Starting packet capture to monitor PostgreSQL server: {postgres_host}:{postgres_port}")
            return await self.start_local_capture(postgres_host, postgres_port, step_identifier=step_identifier)
        except Exception as e:
            logger.error(f"PostgreSQL capture failed: {str(e)}")
            raise Exception(f"PostgreSQL抓包启动失败: {str(e)}")

    async def stop_capture(self) -> str:
        """停止PostgreSQL数据包捕获"""
        try:
            if not self.is_capturing:
                logger.warning("PostgreSQL packet capture is not running")
                return self.current_file or ""

            # 停止本地tcpdump服务
            if self.local_tcpdump_service.is_capturing:
                capture_file = await self.local_tcpdump_service.stop_capture()
                logger.info(f"Local PostgreSQL capture stopped: {capture_file}")

                # 检查文件大小，如果太小则删除
                if capture_file and os.path.exists(capture_file):
                    file_size = os.path.getsize(capture_file)
                    if file_size <= 24:  # pcap文件头大小，表示没有实际数据包
                        logger.warning(f"PostgreSQL capture file is empty (only {file_size} bytes), deleting it")
                        try:
                            os.remove(capture_file)
                            logger.info(f"Deleted empty capture file: {capture_file}")
                        except Exception as e:
                            logger.error(f"Failed to delete empty capture file: {e}")
                        capture_file = None  # 设置为None，这样就不会返回文件名
                    else:
                        # 只有在文件不会被删除的情况下才保存到数据库
                        try:
                            await self._save_capture_file_to_db(capture_file)
                            logger.info(f"PostgreSQL capture file saved to database: {capture_file}")
                        except Exception as e:
                            logger.error(f"Failed to save capture file to database: {e}")
                            # 如果保存到数据库失败，不影响返回文件路径

                self.is_capturing = False
                self.current_file = None
                
                # 返回相对路径（不包含captures/前缀），这样下载API就能正确处理
                if capture_file:
                    filename = os.path.basename(capture_file)
                    logger.info(f"PostgreSQL抓包成功完成:")
                    logger.info(f"  - 文件路径: {capture_file}")
                    logger.info(f"  - 文件名: {filename}")
                    logger.info(f"  - 文件大小: {os.path.getsize(capture_file)} bytes")
                    logger.info(f"  - 文件存在: {os.path.exists(capture_file)}")
                    return filename
                else:
                    logger.error("PostgreSQL抓包失败: 没有生成有效的抓包文件")
                    return ""
            else:
                logger.warning("No local capture process found")
                self.is_capturing = False
                self.current_file = None
                return ""

        except Exception as e:
            logger.error(f"Failed to stop PostgreSQL capture: {str(e)}")
            self.is_capturing = False
            self.current_file = None
            raise

    def get_capture_status(self) -> Dict[str, Any]:
        """获取抓包状态"""
        return {
            'is_capturing': self.is_capturing,
            'current_file': self.current_file,
            'capture_type': 'local',
            'postgres_port': self.postgres_port
        }

    async def start_smart_capture(self, target_host: str = None, target_port: int = None, server_config_id: int = None, *, step_identifier: str | None = None) -> str:
        """智能抓包 - 兼容性方法，实际调用本地抓包，并支持步骤标识"""
        logger.info("Smart capture requested, using local capture implementation")
        return await self.start_capture(target_host, target_port, server_config_id, step_identifier=step_identifier)

    async def _save_capture_file_to_db(self, capture_file_path: str):
        """保存抓包文件信息到数据库"""
        try:
            if not os.path.exists(capture_file_path):
                logger.warning(f"Capture file does not exist: {capture_file_path}")
                return

            file_size = os.path.getsize(capture_file_path)
            filename = os.path.basename(capture_file_path)

            # 从当前抓包配置中获取目标主机和端口信息
            # 这里使用默认值，实际应该从抓包时保存的配置中获取
            target_host = getattr(self, '_current_target_host', '**************')
            target_port = getattr(self, '_current_target_port', 5432)

            # 使用统一路径管理器获取相对路径，确保数据库中存储的是相对路径
            from utils.path_manager import path_manager
            relative_file_path = path_manager.get_relative_capture_path(filename)

            capture_data = CaptureFileCreate(
                filename=filename,
                file_path=relative_file_path,  # 使用相对路径
                file_size=file_size,
                database_type="postgresql",
                target_host=target_host,
                target_port=target_port,
                description=f"PostgreSQL packet capture from {target_host}:{target_port}"
            )

            file_id = await capture_file_service.save_capture_file(capture_data)
            logger.info(f"Saved PostgreSQL capture file to database with ID: {file_id}")

        except Exception as e:
            logger.error(f"Failed to save capture file to database: {e}")
            # 不抛出异常，避免影响抓包流程
