"""
本地tcpdump执行服务
用于在本地执行tcpdump抓包
"""

import os
import asyncio
import subprocess
import signal
import logging
from typing import Optional, Dict, Any
from datetime import datetime
from utils.local_network_utils import LocalNetworkUtils
from utils.path_manager import path_manager

logger = logging.getLogger(__name__)

class LocalTcpdumpService:
    """本地tcpdump执行服务"""
    
    def __init__(self, capture_dir: str = None):
        # 使用统一路径管理器
        if capture_dir is None:
            capture_dir = path_manager.get_captures_dir()
        self.capture_dir = capture_dir
        self.tcpdump_process: Optional[subprocess.Popen] = None
        self.current_file: Optional[str] = None
        self.is_capturing = False
        self.current_task_id = None
        
        # 确保抓包目录存在
        os.makedirs(self.capture_dir, exist_ok=True)

    def set_task_id(self, task_id: str):
        """设置当前任务ID"""
        self.current_task_id = task_id
    
    async def start_capture(
        self, 
        database_type: str,
        target_port: Optional[int] = None,
        interface: Optional[str] = None,
        filter_expression: Optional[str] = None,
        step_identifier: Optional[str] = None
    ) -> str:
        """
        启动本地tcpdump抓包
        
        Args:
            database_type: 数据库类型
            target_port: 目标端口（如果不指定，会自动查找空闲端口）
            interface: 网络接口（如果不指定，会自动选择最佳接口）
            filter_expression: 自定义过滤表达式
            step_identifier: 步骤标识符，用于生成唯一的文件名
            
        Returns:
            str: 抓包文件路径
        """
        if self.is_capturing:
            raise Exception("Packet capture is already running")
        
        try:
            # 检查tcpdump是否可用
            if not await self._check_tcpdump_available():
                raise Exception("tcpdump is not available on this system")
            
            # 确定网络接口
            if not interface:
                interface = LocalNetworkUtils.get_best_local_interface()
                if not interface:
                    raise Exception("No suitable network interface found")
            
            # 验证接口是否存在
            if not LocalNetworkUtils.validate_interface_exists(interface):
                raise Exception(f"Network interface '{interface}' does not exist")
            
            # 确定端口
            if not target_port:
                # 根据数据库类型设置默认端口
                default_ports = {
                    'mysql': 3306,
                    'postgresql': 5432,
                    'postgres': 5432,
                    'gaussdb': 5432,
                    'mongodb': 27017,
                    'oracle': 1521
                }
                target_port = default_ports.get(database_type.lower(), 3306)
                logger.info(f"Using default port {target_port} for {database_type} capture")
            
            # 生成抓包文件名 - 包含步骤标识符
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            if step_identifier:
                filename = f"{database_type}_local_capture_{step_identifier}_{timestamp}.pcap"
            else:
                filename = f"{database_type}_local_capture_{timestamp}.pcap"
            capture_file_path = os.path.join(self.capture_dir, filename)
            self.current_file = capture_file_path

            logger.info(f"🔍 LocalTcpdump start_capture:")
            logger.info(f"🔍 timestamp: {timestamp}")
            logger.info(f"🔍 step_identifier: {step_identifier}")
            logger.info(f"🔍 filename: {filename}")
            logger.info(f"🔍 capture_dir: {self.capture_dir}")
            logger.info(f"🔍 capture_file_path: {capture_file_path}")

            # 构建过滤表达式
            if not filter_expression:
                filter_expression = f"tcp port {target_port}"
            
            # 构建tcpdump命令
            tcpdump_cmd = [
                'tcpdump',
                '-i', interface,
                '-w', self.current_file,
                '-U',  # 逐包写入，避免进程被终止时pcap被截断
                '-s', '0',  # 捕获完整数据包
                '-B', '65536',  # 设置缓冲区大小
                filter_expression
            ]
            
            logger.info(f"Starting local {database_type} packet capture")
            logger.info(f"Interface: {interface}, Port: {target_port}")
            logger.info(f"Filter: {filter_expression}")
            logger.info(f"Output file: {self.current_file}")
            logger.info(f"Command: {' '.join(tcpdump_cmd)}")
            
            # 启动tcpdump进程
            self.tcpdump_process = subprocess.Popen(
                tcpdump_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid  # 创建新的进程组
            )

            # 注册到进程管理器
            try:
                from services.tcpdump_process_manager import tcpdump_manager
                # 从当前调用栈中尝试获取task_id
                task_id = getattr(self, 'current_task_id', f"local_tcpdump_{self.tcpdump_process.pid}")
                tcpdump_manager.register_process(
                    pid=self.tcpdump_process.pid,
                    task_id=task_id,
                    command=' '.join(tcpdump_cmd),
                    capture_file=self.current_file,
                    database_type=database_type,
                    is_remote=False
                )
            except Exception as e:
                logger.warning(f"注册tcpdump进程到管理器失败: {e}")
            
            # 等待一下确保进程启动
            await asyncio.sleep(1)
            
            # 检查进程是否正常运行
            if self.tcpdump_process.poll() is not None:
                # 进程已经退出，获取错误信息
                _, stderr = self.tcpdump_process.communicate()
                error_msg = stderr.decode('utf-8') if stderr else "Unknown error"
                logger.error(f"tcpdump process failed to start. Command: {' '.join(tcpdump_cmd)}")
                logger.error(f"Error output: {error_msg}")
                raise Exception(f"tcpdump failed to start: {error_msg}")
            
            # 额外等待确保tcpdump完全启动并开始捕获
            logger.info("tcpdump process started, waiting for it to begin capturing...")
            await asyncio.sleep(2)
            
            # 再次检查进程状态
            if self.tcpdump_process.poll() is not None:
                _, stderr = self.tcpdump_process.communicate()
                error_msg = stderr.decode('utf-8') if stderr else "Unknown error"
                raise Exception(f"tcpdump process exited unexpectedly: {error_msg}")
            
            self.is_capturing = True
            logger.info(f"Local {database_type} packet capture started successfully")
            logger.info(f"Process PID: {self.tcpdump_process.pid}")
            logger.info(f"Capture file: {self.current_file}")
            
            return self.current_file
            
        except Exception as e:
            logger.error(f"Failed to start local packet capture: {str(e)}")
            await self._cleanup()
            raise
    
    async def stop_capture(self) -> Optional[str]:
        """
        停止抓包
        
        Returns:
            str: 抓包文件路径（相对路径，不包含captures/前缀）
        """
        if not self.is_capturing:
            logger.warning("No packet capture is currently running")
            return None
        
        try:
            if self.tcpdump_process:
                logger.info(f"Stopping tcpdump process (PID: {self.tcpdump_process.pid})")
                
                # 发送SIGINT优雅停止（向tcpdump发送 Ctrl-C，会写入完整pcap尾部）
                try:
                    os.killpg(os.getpgid(self.tcpdump_process.pid), signal.SIGINT)
                except ProcessLookupError:
                    pass

                # 等待进程结束并给文件系统时间flush
                try:
                    await asyncio.wait_for(
                        asyncio.create_task(self._wait_for_process_end()),
                        timeout=8.0
                    )
                except asyncio.TimeoutError:
                    logger.warning("tcpdump process did not stop on SIGINT, sending SIGTERM")
                    try:
                        os.killpg(os.getpgid(self.tcpdump_process.pid), signal.SIGTERM)
                    except ProcessLookupError:
                        pass
                    try:
                        await asyncio.wait_for(
                            asyncio.create_task(self._wait_for_process_end()),
                            timeout=5.0
                        )
                    except asyncio.TimeoutError:
                        logger.warning("tcpdump process did not stop on SIGTERM, forcing SIGKILL")
                        try:
                            os.killpg(os.getpgid(self.tcpdump_process.pid), signal.SIGKILL)
                        except ProcessLookupError:
                            pass
                        await asyncio.sleep(1)

                # 额外等待确保pcap写入完成
                await asyncio.sleep(0.5)
            
            # 检查抓包文件
            captured_file = self.current_file
            if captured_file and os.path.exists(captured_file):
                file_size = os.path.getsize(captured_file)
                logger.info(f"Packet capture completed: {captured_file} ({file_size} bytes)")
                
                # 如果文件太小（可能只有pcap头），记录警告
                if file_size < 100:
                    logger.warning(f"Capture file is very small ({file_size} bytes), may contain no actual packets")
                
                # 返回完整路径，便于任务系统处理
                filename = os.path.basename(captured_file)

                logger.info(f"🔍 LocalTcpdump stop_capture:")
                logger.info(f"🔍 captured_file: {captured_file}")
                logger.info(f"🔍 filename: {filename}")
                logger.info(f"🔍 文件是否存在: {os.path.exists(captured_file)}")
                if os.path.exists(captured_file):
                    logger.info(f"🔍 文件大小: {os.path.getsize(captured_file)} bytes")

                logger.info(f"本地tcpdump抓包成功: {captured_file} ({file_size} bytes)")
                return captured_file
            else:
                logger.error(f"本地tcpdump抓包失败: 文件未生成或不存在")
                logger.error(f"  - 预期文件路径: {captured_file}")
                logger.error(f"  - 当前工作目录: {os.getcwd()}")
                logger.error(f"  - tcpdump进程状态: {self.tcpdump_process.poll() if self.tcpdump_process else 'None'}")
                if self.tcpdump_process:
                    try:
                        stdout, stderr = self.tcpdump_process.communicate(timeout=1)
                        if stderr:
                            logger.error(f"  - tcpdump错误输出: {stderr.decode('utf-8', errors='ignore')}")
                    except Exception as e:
                        logger.error(f"  - 无法获取tcpdump输出: {e}")
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to stop local packet capture: {str(e)}")
            return None
        finally:
            await self._cleanup()
    
    async def _wait_for_process_end(self):
        """等待进程结束"""
        if self.tcpdump_process:
            while self.tcpdump_process.poll() is None:
                await asyncio.sleep(0.1)
    
    async def _cleanup(self):
        """清理资源"""
        # 从进程管理器中注销
        if self.tcpdump_process:
            try:
                from services.tcpdump_process_manager import tcpdump_manager
                tcpdump_manager.unregister_process(self.tcpdump_process.pid)
            except Exception as e:
                logger.warning(f"从进程管理器注销进程失败: {e}")

        self.tcpdump_process = None
        self.is_capturing = False
    
    async def _check_tcpdump_available(self) -> bool:
        """检查tcpdump是否可用"""
        try:
            result = subprocess.run(
                ['tcpdump', '--version'],
                capture_output=True,
                text=True,
                timeout=5
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            return False
    
    def get_capture_status(self) -> Dict[str, Any]:
        """
        获取抓包状态
        
        Returns:
            Dict: 抓包状态信息
        """
        status = {
            'is_capturing': self.is_capturing,
            'current_file': self.current_file,
            'process_pid': self.tcpdump_process.pid if self.tcpdump_process else None,
            'process_running': False
        }
        
        if self.tcpdump_process:
            status['process_running'] = self.tcpdump_process.poll() is None
        
        return status
    
    async def force_stop(self):
        """强制停止抓包"""
        if self.tcpdump_process:
            try:
                os.killpg(os.getpgid(self.tcpdump_process.pid), signal.SIGKILL)
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Error force stopping tcpdump: {str(e)}")
        
        await self._cleanup()
