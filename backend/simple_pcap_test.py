#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的PostgreSQL抓包文件验证测试
"""

import os
import sys
import logging
from datetime import datetime

# 添加backend目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_file_validation():
    """测试文件验证逻辑"""
    logger.info("=" * 60)
    logger.info("测试PostgreSQL抓包文件验证逻辑")
    logger.info("=" * 60)
    
    try:
        # 导入路径管理器
        from utils.path_manager import path_manager
        
        # 测试路径解析
        test_filename = "postgres_local_capture_20250910_192757.pcap"
        resolved_path = path_manager.resolve_capture_file_path(test_filename)
        
        logger.info(f"测试文件名: {test_filename}")
        logger.info(f"解析路径: {resolved_path}")
        logger.info(f"文件存在: {os.path.exists(resolved_path)}")
        
        # 检查captures目录
        captures_dir = path_manager.get_captures_dir()
        logger.info(f"抓包目录: {captures_dir}")
        logger.info(f"目录存在: {os.path.exists(captures_dir)}")
        
        if os.path.exists(captures_dir):
            files = os.listdir(captures_dir)
            postgres_files = [f for f in files if f.startswith("postgres_local_capture")]
            logger.info(f"PostgreSQL抓包文件数量: {len(postgres_files)}")
            
            for f in postgres_files[-5:]:  # 显示最近5个文件
                full_path = os.path.join(captures_dir, f)
                size = os.path.getsize(full_path)
                mtime = datetime.fromtimestamp(os.path.getmtime(full_path))
                logger.info(f"  - {f}: {size} bytes, 修改时间: {mtime}")
        
        # 测试文件验证函数
        logger.info("\n测试文件验证函数:")
        
        # 模拟验证逻辑
        def validate_capture_file(capture_file: str, database_type: str):
            """模拟文件验证逻辑"""
            if not capture_file:
                return {"valid": False, "error": "抓包文件路径为空"}
            
            # 如果是文件名，构建完整路径
            if not os.path.isabs(capture_file):
                full_path = path_manager.resolve_capture_file_path(capture_file)
            else:
                full_path = capture_file
            
            logger.info(f"验证{database_type}抓包文件: {capture_file} -> {full_path}")
            
            # 检查文件是否存在
            if not os.path.exists(full_path):
                return {"valid": False, "error": f"抓包文件不存在: {full_path}"}
            
            # 检查文件大小
            file_size = os.path.getsize(full_path)
            if file_size <= 24:  # pcap文件头大小
                return {"valid": False, "error": f"抓包文件为空或过小 ({file_size} bytes): {full_path}"}
            
            # 检查文件是否可读
            if not os.access(full_path, os.R_OK):
                return {"valid": False, "error": f"抓包文件无法读取: {full_path}"}
            
            logger.info(f"{database_type}抓包文件验证成功: {full_path} ({file_size} bytes)")
            return {"valid": True, "file_path": full_path, "file_size": file_size}
        
        # 测试存在的文件
        if postgres_files:
            test_file = postgres_files[-1]  # 最新的文件
            result = validate_capture_file(test_file, "postgresql")
            logger.info(f"验证结果: {result}")
            
            if result["valid"]:
                logger.info("✅ 文件验证逻辑正常工作")
                return True
            else:
                logger.error(f"❌ 文件验证失败: {result['error']}")
                return False
        else:
            logger.warning("没有找到PostgreSQL抓包文件进行测试")
            
            # 测试不存在的文件
            fake_file = "non_existent_file.pcap"
            result = validate_capture_file(fake_file, "postgresql")
            logger.info(f"不存在文件的验证结果: {result}")
            
            if not result["valid"]:
                logger.info("✅ 不存在文件的验证逻辑正常工作")
                return True
            else:
                logger.error("❌ 不存在文件的验证逻辑异常")
                return False
        
    except Exception as e:
        logger.error(f"测试过程中发生异常: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_pcap_validation_service():
    """测试抓包验证服务"""
    logger.info("\n" + "=" * 60)
    logger.info("测试抓包验证服务")
    logger.info("=" * 60)
    
    try:
        from services.pcap_validation_service import PCAPValidationService
        
        validator = PCAPValidationService()
        
        # 获取一个存在的pcap文件进行测试
        from utils.path_manager import path_manager
        captures_dir = path_manager.get_captures_dir()
        
        if os.path.exists(captures_dir):
            files = os.listdir(captures_dir)
            postgres_files = [f for f in files if f.startswith("postgres_local_capture") and f.endswith(".pcap")]
            
            if postgres_files:
                test_file = postgres_files[-1]  # 最新的文件
                full_path = os.path.join(captures_dir, test_file)
                
                logger.info(f"测试验证服务，文件: {test_file}")
                result = validator.validate_pcap_file(full_path)
                
                logger.info(f"验证状态: {result.status.value}")
                logger.info(f"验证成功: {result.success}")
                logger.info(f"找到SQL数量: {result.total_sql_found}")
                logger.info(f"错误信息: {result.error_message}")
                
                if result.status.value != "file_not_found":
                    logger.info("✅ 抓包验证服务正常工作")
                    return True
                else:
                    logger.error("❌ 抓包验证服务仍报告文件不存在")
                    return False
            else:
                logger.warning("没有找到PostgreSQL抓包文件进行验证服务测试")
                return True
        else:
            logger.warning("抓包目录不存在")
            return False
            
    except Exception as e:
        logger.error(f"测试抓包验证服务时发生异常: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    logger.info("开始PostgreSQL抓包文件生成bug修复验证")
    
    success1 = test_file_validation()
    success2 = test_pcap_validation_service()
    
    if success1 and success2:
        logger.info("✅ 所有测试通过：PostgreSQL抓包文件验证逻辑修复成功")
        return 0
    else:
        logger.error("❌ 测试失败：仍存在问题需要修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())
