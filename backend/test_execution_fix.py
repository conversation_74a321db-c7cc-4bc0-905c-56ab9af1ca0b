#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试执行器修复效果 - 模拟测试用例执行场景
"""

import asyncio
import os
import sys
import logging
from datetime import datetime

# 添加backend目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_smart_capture_controller():
    """测试智能抓包控制器的文件验证逻辑"""
    logger.info("=" * 60)
    logger.info("测试智能抓包控制器文件验证逻辑")
    logger.info("=" * 60)
    
    try:
        # 导入必要的模块
        from services.test_case_execution_agent_service import SmartPacketCaptureController
        from utils.path_manager import path_manager
        
        # 创建控制器实例
        controller = SmartPacketCaptureController()
        
        # 模拟SQL语句
        test_sql_statements = [
            "CREATE TABLE test_table (id SERIAL PRIMARY KEY, name VARCHAR(100));",
            "INSERT INTO test_table (name) VALUES ('test1'), ('test2');",
            "SELECT * FROM test_table;",
            "DROP TABLE test_table;"
        ]
        
        # 模拟配置ID（使用一个不存在的配置来测试错误处理）
        fake_config_id = 999999
        
        logger.info("测试不存在配置的错误处理...")
        result = await controller.execute_sql_with_capture(
            sql_statements=test_sql_statements,
            database_type="postgresql",
            config_id=fake_config_id,
            use_c_executor=False
        )
        
        logger.info(f"执行结果: {result}")
        
        if not result.get("success", True):
            logger.info("✅ 错误处理正常：不存在的配置被正确拒绝")
        else:
            logger.warning("⚠️ 错误处理可能有问题：不存在的配置没有被拒绝")
        
        return True
        
    except Exception as e:
        logger.error(f"测试智能抓包控制器时发生异常: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

async def test_file_validation_method():
    """测试文件验证方法"""
    logger.info("\n" + "=" * 60)
    logger.info("测试文件验证方法")
    logger.info("=" * 60)
    
    try:
        from services.test_case_execution_agent_service import TestCaseExecutionAgentService
        
        # 创建执行器实例
        executor = TestCaseExecutionAgentService()
        
        # 测试存在的文件
        from utils.path_manager import path_manager
        captures_dir = path_manager.get_captures_dir()
        
        if os.path.exists(captures_dir):
            files = os.listdir(captures_dir)
            postgres_files = [f for f in files if f.startswith("postgres_local_capture") and f.endswith(".pcap")]
            
            if postgres_files:
                test_file = postgres_files[-1]  # 最新的文件
                logger.info(f"测试存在的文件: {test_file}")
                
                result = await executor._validate_capture_file_existence(test_file, "postgresql")
                logger.info(f"验证结果: {result}")
                
                if result["valid"]:
                    logger.info("✅ 存在文件的验证正常")
                else:
                    logger.error(f"❌ 存在文件的验证失败: {result['error']}")
                    return False
            
        # 测试不存在的文件
        fake_file = "non_existent_file_12345.pcap"
        logger.info(f"测试不存在的文件: {fake_file}")
        
        result = await executor._validate_capture_file_existence(fake_file, "postgresql")
        logger.info(f"验证结果: {result}")
        
        if not result["valid"]:
            logger.info("✅ 不存在文件的验证正常")
        else:
            logger.error("❌ 不存在文件的验证异常")
            return False
        
        # 测试空文件名
        logger.info("测试空文件名")
        result = await executor._validate_capture_file_existence("", "postgresql")
        logger.info(f"验证结果: {result}")
        
        if not result["valid"]:
            logger.info("✅ 空文件名的验证正常")
        else:
            logger.error("❌ 空文件名的验证异常")
            return False
        
        # 测试None文件名
        logger.info("测试None文件名")
        result = await executor._validate_capture_file_existence(None, "postgresql")
        logger.info(f"验证结果: {result}")
        
        if not result["valid"]:
            logger.info("✅ None文件名的验证正常")
        else:
            logger.error("❌ None文件名的验证异常")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"测试文件验证方法时发生异常: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

async def test_capture_validation_manager():
    """测试抓包验证管理器的路径解析"""
    logger.info("\n" + "=" * 60)
    logger.info("测试抓包验证管理器路径解析")
    logger.info("=" * 60)
    
    try:
        from services.capture_validation_manager import CaptureValidationManager
        from utils.path_manager import path_manager
        
        # 创建验证管理器
        manager = CaptureValidationManager()
        
        # 创建测试任务
        task_id = f"test_task_{int(datetime.now().timestamp())}"
        task = manager.start_capture_task(
            task_id=task_id,
            database_type="postgresql",
            expected_operations=["SELECT", "INSERT", "UPDATE", "DELETE"]
        )
        
        # 添加一些测试文件（包括存在和不存在的）
        captures_dir = path_manager.get_captures_dir()
        if os.path.exists(captures_dir):
            files = os.listdir(captures_dir)
            postgres_files = [f for f in files if f.startswith("postgres_local_capture") and f.endswith(".pcap")]
            
            if postgres_files:
                # 添加存在的文件
                existing_file = postgres_files[-1]
                manager.add_pcap_file_to_task(task_id, existing_file)
                logger.info(f"添加存在的文件: {existing_file}")
            
        # 添加不存在的文件
        fake_file = "fake_postgres_capture.pcap"
        manager.add_pcap_file_to_task(task_id, fake_file)
        logger.info(f"添加不存在的文件: {fake_file}")
        
        # 执行验证
        logger.info("执行批量验证...")
        validation_result = manager.complete_capture_task(task_id)
        
        if validation_result:
            summary = validation_result.get('summary', {})
            logger.info(f"验证摘要: {summary}")
            
            file_results = validation_result.get('file_results', [])
            for file_result in file_results:
                logger.info(f"文件结果: {file_result}")
            
            # 检查是否正确处理了不存在的文件
            failed_files = [r for r in file_results if not r.get('success', True)]
            if failed_files:
                logger.info(f"✅ 正确识别了 {len(failed_files)} 个失败的文件")
            else:
                logger.warning("⚠️ 没有识别出失败的文件，可能有问题")
            
            return True
        else:
            logger.error("❌ 验证管理器返回空结果")
            return False
        
    except Exception as e:
        logger.error(f"测试抓包验证管理器时发生异常: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

async def main():
    """主函数"""
    logger.info("开始测试执行器修复效果")
    
    success1 = await test_smart_capture_controller()
    success2 = await test_file_validation_method()
    success3 = await test_capture_validation_manager()
    
    if success1 and success2 and success3:
        logger.info("🎉 所有测试通过：执行器修复成功！")
        logger.info("修复内容包括：")
        logger.info("  1. ✅ 添加了文件存在性验证")
        logger.info("  2. ✅ 改进了路径解析逻辑")
        logger.info("  3. ✅ 增强了错误处理和日志")
        logger.info("  4. ✅ 修复了抓包验证服务")
        return 0
    else:
        logger.error("❌ 部分测试失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
